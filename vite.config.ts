import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [
    vue({
      template: { transformAssetUrls }
    }),
    vuetify({
      autoImport: true,
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  base: '/',
  build: {
    rollupOptions: {
      input: {
        main: './index.html'
      }
    },
    minify: 'esbuild',
    target: 'esnext',
    // Ensure proper handling of i18n messages and locale files
    assetsInlineLimit: 0,
    // Ensure locale JSON files are copied to dist
    copyPublicDir: true
  }
})
