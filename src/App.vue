<template>
  <v-app>
    <AppHeader>
      <template #mainheader>
        <TopHeader />
        <MainHeader />
      </template>
    </AppHeader>
    
    <main class="main-content">
      <section class="hero-section" v-if="showHeroBanner">
        <HeroBanner 
          :title="t('hero.title')"
          :cta="t('hero.cta')"
          image="/images/hero-bg.jpg"
        />
      </section>
      
      <router-view />
      
    </main>
    
    <AppFooter />
  </v-app>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

import AppHeader from './components/layout/AppHeader.vue'
import TopHeader from './components/layout/TopHeader.vue'
import MainHeader from './components/layout/ManHeader.vue'
import HeroBanner from './components/ui/HeroBanner.vue'
import AppFooter from './components/layout/AppFooter.vue'
import TranslationTest from './components/debug/TranslationTest.vue'

const route = useRoute()
const { t } = useI18n()

const noHeroPages = ['/chairman-speech', '/']

const showHeroBanner = computed(() => {
  return !noHeroPages.includes(route.path.toLowerCase())
})


</script>

<style>
@import '@fortawesome/fontawesome-free/css/all.css';
</style>

<style scoped>
.v-app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.hero-section {
  padding: 80px 0;
  min-height: 400px;
}

.history-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.main-content {
  flex: 1;
  margin-top: 124px; 
  width: 100%;
}
</style>