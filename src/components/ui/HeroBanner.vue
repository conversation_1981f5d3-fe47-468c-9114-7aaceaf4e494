<template>
  <section class="external-banner" :style="{ backgroundImage: `url(${defaultImage})` }">
    <div class="banner-content">
      <div class="banner-title-wrapper" :class="{ 'vertical-text': isMongolian }">
        <h1 class="banner-title">{{ defaultTitle }}</h1>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { currentLocale } from '@/plugins/i18n';

const props = defineProps<{
  title: string
  image: string
}>()

const { t } = useI18n();

const defaultTitle = computed(() => props.title || t('Default Title'))
const defaultImage = computed(() => props.image || '/images/default.jpg')
const isMongolian = computed(() => currentLocale.value  === 'mn');
</script>


<!-- <script setup lang="ts">
import { ref, computed} from 'vue';
import { useScopedI18n } from '@/composables/useScopedI18n'
import { setLocale, currentLocale, AvailableLocales } from '@/plugins/i18n';

const props = defineProps<{
  title: string
  image: string
}>()

const defaultTitle = computed(() => props.title || t('Default Title'))
const defaultImage = computed(() => props.image || '/images/default.jpg')

const translations: Record<string, any> = { en, mn,ch };
const locale = ref<string>('en');


const getTranslations = () => {
  return translations[locale.value];
};


const { t } = useScopedI18n('nav', getTranslations);


const isMongolian = computed(() => locale.value === 'mn');


</script> -->

<style scoped>
.external-banner {
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 75vh;
  padding: 0 2rem;
}

.banner-content {
  max-width: 100%;
  opacity: 0;
  transform: translateX(-50px);
  animation: fadeInFromLeft 1.2s ease-out 0.3s forwards;
}

.banner-title-wrapper {
  max-width: 800px;
  text-align: left;
  width: 100%;
  margin-top: 120px;
  margin-left: 50px;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: 400;
  color: white;
  margin: 0 0 1.5rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  line-height: 1.1;
  position: relative;
}

.banner-title::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    to right,
    #f7931e 0%,
    #f7931e 50%,
    #ff4444 50%,
    #ff4444 100%
  );
}

@keyframes fadeInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .banner-title {
    font-size: 2.5rem;
  }

  .external-banner {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .banner-title {
    font-size: 2rem;
  }
}


.vertical-text {
  writing-mode: vertical-lr;
  text-orientation: sideways;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 0.5rem;
  max-height: 200px;
    overflow: hidden;
    word-break: break-word;
}
.vertical-text .banner-title::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    to right,
    #f7931e 0%,
    #f7931e 50%,
    #ff4444 50%,
    #ff4444 100%
  );
}
</style>
