<template>
  <div style="position: fixed; top: 10px; right: 10px; background: white; border: 2px solid red; padding: 10px; z-index: 9999;">
    <h3>Translation Debug</h3>
    <p><strong>Current Locale:</strong> {{ locale }}</p>
    <p><strong>Available Messages:</strong> {{ Object.keys(messages) }}</p>
    <p><strong>Test Translation:</strong> {{ t('home.title') }}</p>
    <p><strong>Main Header About:</strong> {{ t('mainHeader.aboutMonte') }}</p>
    <p><strong>Raw Messages Sample:</strong></p>
    <pre>{{ JSON.stringify(currentMessages, null, 2) }}</pre>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

const { t, locale, messages } = useI18n()

const currentMessages = computed(() => {
  const msgs = messages.value as Record<string, any>
  return {
    homeTitle: msgs[locale.value]?.home?.title,
    mainHeaderAbout: msgs[locale.value]?.mainHeader?.aboutMonte,
    allKeys: Object.keys(msgs[locale.value] || {})
  }
})

console.log('Translation Test - Current locale:', locale.value)
console.log('Translation Test - Available messages:', Object.keys(messages.value))
console.log('Translation Test - Sample translation:', t('home.title'))
console.log('Translation Test - Messages structure:', messages.value[locale.value])
</script>
