import { createI18n } from 'vue-i18n'

export const SUPPORT_LOCALES = ['en', 'mn', 'ch'] as const
export type AvailableLocales = (typeof SUPPORT_LOCALES)[number]

// Function to load locale messages dynamically
async function loadLocaleMessages(locale: string) {
  console.log(`Loading locale messages for: ${locale}`)
  try {
    const response = await fetch(`/locales/${locale}.json`)
    if (!response.ok) {
      console.warn(`Failed to load locale ${locale}:`, response.status)
      // Return basic fallback messages
      return getFallbackMessages(locale)
    }
    const messages = await response.json()
    console.log(`Successfully loaded ${locale} locale with ${Object.keys(messages).length} top-level keys`)
    return messages || getFallbackMessages(locale)
  } catch (error) {
    console.error(`Error loading locale ${locale}:`, error)
    return getFallbackMessages(locale)
  }
}

// Fallback messages for when locale files fail to load
function getFallbackMessages(locale: string) {
  const fallback = {
    nav: {
      'Menu': locale === 'mn' ? 'ᠴᠡᠰ' : locale === 'ch' ? '菜单' : 'Menu',
      'Monte Group': locale === 'mn' ? 'ᠮᠣᠨᠲᠧ ᠪᠦᠯᠦᠭ' : locale === 'ch' ? '蒙特集团' : 'Monte Group',
      'Language': locale === 'mn' ? 'ᠬᠡᠯᠡ' : locale === 'ch' ? '语言' : 'Language',
      'English': locale === 'mn' ? 'ᠠᠩᠭᠯᠢ ᠬᠡᠯᠡ' : locale === 'ch' ? '英语' : 'English',
      'Chinese': locale === 'mn' ? 'ᠬᠢᠲᠠᠳ ᠬᠡᠯᠡ' : locale === 'ch' ? '中文' : 'Chinese',
      'Mongolian': locale === 'mn' ? 'ᠮᠣᠩᠭᠣᠯ ᠤᠨ ᠬᠡᠯᠡ' : locale === 'ch' ? '蒙古语' : 'Mongolian'
    }
  }
  return fallback
}

// Create i18n instance with empty messages initially
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  globalInjection: true,
  messages: {}, // Start with empty messages
  // These options are critical for proper rendering in production
  escapeHtml: false,
  warnHtmlMessage: false,
  runtimeOnly: false,
  // Add this to ensure messages are available in production
  allowComposition: true,
  missingWarn: false,
  fallbackWarn: false
})

export const currentLocaleValue = i18n.global.locale.value
export const currentLocale = i18n.global.locale

export const setLocale = async (locale: AvailableLocales) => {
  try {
    console.log(`Setting locale to: ${locale}`)
    // Load locale messages if not already loaded
    const messages = i18n.global.messages.value as Record<string, Record<string, unknown>>
    console.log('Current messages keys:', Object.keys(messages))

    if (!messages[locale] || Object.keys(messages[locale] || {}).length === 0) {
      console.log(`Loading messages for locale: ${locale}`)
      const localeMessages = await loadLocaleMessages(locale)
      i18n.global.setLocaleMessage(locale, localeMessages)
      console.log(`Messages set for locale: ${locale}`)
    } else {
      console.log(`Messages already loaded for locale: ${locale}`)
    }

    i18n.global.locale.value = locale
    localStorage.setItem('lang', locale)
    console.log(`Locale successfully set to: ${locale}`)
    return locale
  } catch (error) {
    console.error('Error setting locale:', error)
    return 'en'
  }
}

// Initialize with default locale
export const initializeI18n = async () => {
  try {
    console.log('Initializing i18n with English locale...')
    // Load English as default
    const enMessages = await loadLocaleMessages('en')
    i18n.global.setLocaleMessage('en', enMessages)
    console.log('i18n initialized successfully with English messages')
  } catch (error) {
    console.error('Error initializing i18n:', error)
  }
}

export default i18n;

