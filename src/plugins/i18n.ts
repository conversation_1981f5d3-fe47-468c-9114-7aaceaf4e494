// import { createI18n } from 'vue-i18n'
// import { en, mn, ch } from './json'
// // const en = {
// //   "nav": {
// //     "Group Website Group": "Group Website Group",
// //     "Staff Channel": "Staff Channel",
// //     "Chinese": "Chinese",
// //     "English": "English",
// //     "Mongolian": "Mongolian",
// //     "Close": "Close",
// //     "Monte Group": "Monte Group",
// //     "Apt": "Apt",
// //     "Collaborative Office": "Collaborative Office",
// //     "Corporate Email": "Corporate Email",
// //     "Menu": "Menu",
// //     "Language": "Language"
// //   }
// // }

// // const mn = {
// //   "nav": {
// //     "Group Website Group": "ᠪᠦᠯᠦᠭ ᠦᠨ ᠦᠨᠳᠦᠰᠦᠲᠡᠨ ᠦ ᠪᠦᠯᠦᠭ",
// //     "Staff Channel": "ᠠᠵᠢᠯᠴᠢᠨ ᠦ ᠰᠤᠪᠤᠷᠭᠠ",
// //     "Chinese": "ᠬᠢᠲᠠᠳ ᠬᠡᠯᠡ",
// //     "English": "ᠠᠩᠭᠯᠢ ᠬᠡᠯᠡ",
// //     "Mongolian": "ᠮᠣᠩᠭᠣᠯ ᠤᠨ ᠬᠡᠯᠡ",
// //     "Close": "ᠬᠠᠭᠠᠬᠤ",
// //     "Monte Group": "ᠮᠣᠨᠲᠧ ᠪᠦᠯᠦᠭ",
// //     "Apt": "ᠠᠫᠲ ᠠᠪᠯᠢ",
// //     "Collaborative Office": "ᠬᠠᠮᠲᠤ ᠠᠵᠢᠯᠯᠠᠬᠤ ᠠᠯᠪᠠ",
// //     "Corporate Email": "ᠬᠠᠮᠲᠤ ᠬᠦᠮᠦᠨ ᠦ ᠡᠯᠧᠺᠲ᠋ᠷᠣᠨ ᠱᠤᠭᠤᠮ",
// //     "Menu": "ᠴᠡᠰ",
// //     "Language": "ᠬᠡᠯᠡ"
// //   }
// // }

// // const ch = {
// //   "nav": {
// //     "Group Website Group": "集团网站组",
// //     "Staff Channel": "员工通道",
// //     "Chinese": "中文",
// //     "English": "英语",
// //     "Mongolian": "蒙古语",
// //     "Close": "关闭",
// //     "Monte Group": "蒙特集团",
// //     "Apt": "Apt公寓",
// //     "Collaborative Office": "协同办公",
// //     "Corporate Email": "企业邮箱",
// //     "Menu": "菜单",
// //     "Language": "语言"
// //   }
// // }


// export const SUPPORT_LOCALES = ['en', 'mn', 'ch'] as const
// export type AvailableLocales = (typeof SUPPORT_LOCALES)[number]

// // Create i18n instance with hard-coded messages
// const i18n = createI18n({
//   legacy: false,
//   locale: 'en',
//   fallbackLocale: 'en',
//   globalInjection: true,
//   messages: { en, mn, ch },
//   // Add these options to fix Mongolian character rendering in production
//   escapeHtml: false,
//   warnHtmlMessage: false,
//   runtimeOnly: false
// })

// export const currentLocaleValue = i18n.global.locale.value
// export const currentLocale = i18n.global.locale

// export const setLocale = async (locale: AvailableLocales) => {
//   try {
//     i18n.global.locale.value = locale
//     localStorage.setItem('lang', locale)
//     return locale
//   } catch (error) {
//     console.error('Error setting locale:', error)
//     return 'en'
//   }
// }

// export default i18n;


import { createI18n } from 'vue-i18n'
import { en, mn, ch } from './json'

export const SUPPORT_LOCALES = ['en', 'mn', 'ch'] as const
export type AvailableLocales = (typeof SUPPORT_LOCALES)[number]

// Create i18n instance with messages from imported JSON
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  globalInjection: true,
  messages: { en, mn, ch },
  // These options are critical for proper rendering in production
  escapeHtml: false,
  warnHtmlMessage: false,
  runtimeOnly: false,
  // Add this to ensure messages are available in production
  allowComposition: true,
  missingWarn: false,
  fallbackWarn: false
})

export const currentLocaleValue = i18n.global.locale.value
export const currentLocale = i18n.global.locale

export const setLocale = async (locale: AvailableLocales) => {
  try {
    i18n.global.locale.value = locale
    localStorage.setItem('lang', locale)
    return locale
  } catch (error) {
    console.error('Error setting locale:', error)
    return 'en'
  }
}

export default i18n;

