import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './assets/main.css'
import App from './App.vue'
import router from './router'
import vuetify from './plugins/vuetify'
import intersect from './directives/v-intersect'
import i18n, { setLocale, initializeI18n } from './plugins/i18n'

(async () => {
  console.log('Starting app initialization...')

  // Initialize i18n with default locale
  console.log('Initializing i18n...')
  await initializeI18n()

  // Get saved language or default to English
  let savedLang = 'en'
  try {
    savedLang = localStorage.getItem('lang') || 'en'
  } catch (e) {
    console.warn('Could not access localStorage:', e)
  }

  console.log('Setting locale to:', savedLang)
  // Set locale
  await setLocale(savedLang as 'en' | 'mn' | 'ch')

  console.log('Creating and mounting app...')
  // Create and mount app
  const app = createApp(App)
  app.directive('intersect', intersect)

  app.use(createPinia())
  app.use(router)
  app.use(vuetify)
  app.use(i18n)
  app.mount("#app")

  console.log('App mounted successfully!')
})();
