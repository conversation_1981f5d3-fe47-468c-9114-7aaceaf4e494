
import { DefineLocaleMessage, DefineDateTimeFormat, DefineNumberFormat } from 'vue-i18n'

declare module 'vue-i18n' {
 
  export interface DefineLocaleMessage {
    dev: {
      title: string
      section: string
      achievements: {
        [key: string]: string[]
      }
    }
  }

 
  export type DefineDateTimeFormat = unknown;


  export type DefineNumberFormat = unknown;
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $t: (key: string) => string
    $i18n: {
      locale: string
      fallbackLocale: string
    }
  }
}