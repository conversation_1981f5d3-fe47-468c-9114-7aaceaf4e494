{
  "name": "monte-app",
  "version": "0.0.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev:vercel": "vercel dev",
    "dev": "vite",
    "build": "vue-tsc --noEmit && vite build",
    "build-only": "vite build",
    "deploy": "gh-pages -d dist",
    "preview": "vite preview",
    "type-check": "vue-tsc --build",
    "clean-build": "rm -rf dist && npm run build",
    "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore",
    "lint:eslint": "eslint . --fix",
    "lint": "run-s lint:*",
    "format": "prettier --write src/",
    "i18n:report": "vue-i18n-extract report --vueFiles './src/**/*.vue' --languageFiles './src/locales/**/*.json'",
    "i18n:missing": "vue-i18n-extract report --vueFiles './src/**/*.vue' --languageFiles './src/locales/**/*.json'",
    "i18n:sync": "tsx scripts/sync-i18n.ts"
  }, thi loclae 
  "dependencies": {
    "@fortawesome/fontawesome-free": "^6.7.2",
    "@fortawesome/fontawesome-svg-core": "^6.7.2",
    "@fortawesome/free-solid-svg-icons": "^6.7.2",
    "@fortawesome/vue-fontawesome": "^3.0.8",
    "@mdi/font": "^7.4.47",
    "@vercel/postgres": "^0.10.0",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "fs": "^0.0.1-security",
    "mammoth": "^1.9.1",
    "path": "^0.12.7",
    "pdfjs-dist": "^5.3.31",
    "pinia": "^3.0.1",
    "vue": "^3.5.13",
    "vue-i18n": "^12.0.0-alpha.2",
    "vue-router": "^4.5.0",
    "vuetify": "^3.8.9"
  },
  "devDependencies": {
    "@intlify/unplugin-vue-i18n": "^6.0.8",
    "@tsconfig/node22": "^22.0.1",
    "@types/node": "^22.14.0",
    "@types/pdfjs-dist": "^2.10.377",
    "@vercel/node": "^3.2.29",
    "@vitejs/plugin-vue": "^5.2.4",
    "@vue/eslint-config-prettier": "^10.2.0",
    "@vue/eslint-config-typescript": "^14.5.0",
    "@vue/tsconfig": "^0.7.0",
    "concurrently": "^8.2.2",
    "eslint": "^9.22.0",
    "eslint-plugin-oxlint": "^0.16.0",
    "eslint-plugin-vue": "~10.0.0",
    "express": "^4.21.2",
    "fs-extra": "^11.3.0",
    "gh-pages": "^6.3.0",
    "glob": "^11.0.2",
    "jiti": "^2.4.2",
    "npm-run-all2": "^7.0.2",
    "oxlint": "^0.16.0",
    "prettier": "3.5.3",
    "terser": "^5.42.0",
    "tsx": "^4.20.3",
    "typescript": "~5.8.0",
    "unplugin-vue-i18n": "^1.0.11",
    "vite": "^6.3.5",
    "vite-plugin-vue-devtools": "^7.7.2",
    "vite-plugin-vuetify": "^2.1.1",
    "vue-i18n-extract": "^2.0.7",
    "vue-tsc": "^2.2.8"
  }
}
